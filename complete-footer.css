/* Complete Modern Footer CSS - ไฟล์เดียวครบทุกอย่าง */

/* CSS Variables สำหรับปรับแต่งง่าย */
:root {
  --footer-bg-image: url(http://mv-tv.com/wp-content/uploads/2024/09/inside-out-2-5422x2160-17820-scaled.jpg);
  --footer-primary: #00c6ff;
  --footer-secondary: #008be5;
  --footer-tertiary: #0056b3;
  --footer-success: #28af64;
  --footer-text-light: #c7c7c7;
  --footer-text-muted: #989799;
  --footer-text-dark: #999;
  --footer-bg-dark: #212120;
  --footer-border: #2a2936;
  --footer-border-light: #303030;
  --footer-white: #fff;
  --footer-black: #000;
  --footer-max-width: 1440px;
  --footer-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Keyframes สำหรับ Animation */
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInLeft {
  from { opacity: 0; transform: translateX(-30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Main Footer Container */
.site-footer {
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.8s ease-out;
}

/* Content Footer - Enhanced */
.content-footer {
  background: var(--footer-bg-image);
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  min-height: 342px;
  padding: 40px 0;
  position: relative;
  overflow: hidden;
}

/* Modern overlay effect */
.content-footer::before {
  content: '';
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: linear-gradient(135deg, 
    rgba(0, 0, 0, 0.85) 0%, 
    rgba(0, 0, 0, 0.7) 30%,
    rgba(0, 0, 0, 0.6) 70%,
    rgba(0, 0, 0, 0.85) 100%);
  backdrop-filter: blur(10px);
  z-index: 1;
}

/* Animated particles effect */
.content-footer::after {
  content: '';
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(0, 198, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 139, 229, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(0, 86, 179, 0.1) 0%, transparent 50%);
  animation: float 6s ease-in-out infinite;
  z-index: 2;
}

/* Width container */
.width-auto {
  width: var(--footer-max-width);
  margin: 0 auto;
  position: relative;
  z-index: 3;
}

/* Grid layout */
.content-footer .width-auto {
  display: grid;
  grid-template-columns: 40% 55%;
  grid-column-gap: 5%;
  align-items: start;
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

/* Content section */
.content-footer .content {
  animation: slideInLeft 0.6s ease-out 0.5s both;
}

.content-footer .content a {
  display: inline-block;
  text-align: center;
  transition: var(--footer-transition);
}

.content-footer .content a:hover {
  transform: translateY(-3px);
  filter: drop-shadow(0 8px 16px rgba(0, 198, 255, 0.3));
}

/* Typography */
.content-footer p {
  font-size: 14px;
  color: var(--footer-text-muted);
  font-family: 'Kanit', sans-serif;
  font-weight: 300;
  line-height: 1.6;
  margin: 15px 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Footer logo */
.footer-site-logo {
  margin: 0 0 30px;
  text-align: center;
  position: relative;
}

.footer-site-logo img {
  width: 300px;
  max-width: 100%;
  height: auto;
  transition: var(--footer-transition);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.footer-site-logo:hover img {
  transform: scale(1.05);
  filter: drop-shadow(0 8px 16px rgba(0, 198, 255, 0.4));
}

/* Title styles */
.t-footer {
  font-size: 18px;
  font-family: 'Kanit', sans-serif;
  font-weight: 400;
  color: var(--footer-white);
  margin: 10px 0 15px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

p.title-t {
  font-size: 24px;
  color: var(--footer-white);
  border-bottom: 2px solid var(--footer-border-light);
  padding-bottom: 10px;
  margin-bottom: 20px;
  font-family: 'Kanit', sans-serif;
  font-weight: 400;
  position: relative;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Modern underline effect */
p.title-t::after {
  content: '';
  position: absolute;
  bottom: -2px; left: 0;
  width: 0; height: 2px;
  background: linear-gradient(90deg, var(--footer-primary), var(--footer-secondary));
  transition: width 0.6s ease;
}

p.title-t:hover::after {
  width: 100%;
}

/* Footer tags section */
.footer-tag {
  margin-top: -25px;
  animation: slideInRight 0.6s ease-out 0.7s both;
}

.f-tag {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 15px;
}

.f-tag a {
  font-size: 14px;
  font-family: 'Kanit', sans-serif;
  font-weight: 400;
  color: var(--footer-text-dark);
  background: var(--footer-bg-dark);
  border-radius: 25px;
  padding: 8px 16px;
  display: inline-flex;
  align-items: center;
  text-decoration: none;
  transition: var(--footer-transition);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
}

/* Modern hover effect for tags */
.f-tag a::before {
  content: '';
  position: absolute;
  top: 0; left: -100%;
  width: 100%; height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.f-tag a:hover::before {
  left: 100%;
}

.f-tag a:hover {
  color: var(--footer-black) !important;
  background: linear-gradient(148deg, var(--footer-primary), var(--footer-secondary), var(--footer-tertiary));
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 198, 255, 0.4);
  border-color: var(--footer-primary);
}

/* Navigation */
footer#colophon nav#site-navigation {
  border-top: 0;
  padding: 20px 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

/* Site info */
.site-info {
  display: block;
  overflow: hidden;
  text-align: center;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(15px);
  position: relative;
}

.site-info::before {
  content: '';
  position: absolute;
  top: 0; left: 0; right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--footer-border), transparent);
}

.site-info p {
  border-top: 1px solid var(--footer-border);
  font-size: 14px;
  color: var(--footer-text-light);
  padding: 15px 20px;
  margin: 0;
  font-family: 'Kanit', sans-serif;
  font-weight: 300;
  line-height: 1.5;
}

.site-info a {
  color: var(--footer-success);
  font-weight: bold;
  font-family: 'Kanit', sans-serif;
  text-decoration: none;
  transition: var(--footer-transition);
  position: relative;
}

.site-info a::after {
  content: '';
  position: absolute;
  bottom: -2px; left: 0;
  width: 0; height: 2px;
  background: var(--footer-primary);
  transition: width 0.3s ease;
}

.site-info a:hover {
  color: var(--footer-primary);
  text-shadow: 0 0 8px rgba(0, 198, 255, 0.5);
}

.site-info a:hover::after {
  width: 100%;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet (768px - 991px) */
@media (max-width: 991px) and (min-width: 768px) {
  .width-auto {
    width: 95%;
    padding: 0 15px;
  }

  .content-footer {
    min-height: 400px;
    padding: 30px 0;
    background-attachment: scroll;
  }

  .content-footer .width-auto {
    grid-template-columns: 1fr;
    grid-row-gap: 30px;
    text-align: center;
  }

  .footer-site-logo img {
    width: 250px;
  }

  p.title-t {
    font-size: 20px;
    text-align: center;
  }

  .t-footer {
    font-size: 16px;
    text-align: center;
  }

  .content-footer p {
    text-align: center;
    font-size: 15px;
  }

  .f-tag {
    justify-content: center;
    gap: 6px;
  }

  .footer-tag {
    margin-top: 0;
  }
}

/* Mobile (max-width: 767px) */
@media (max-width: 767px) {
  .width-auto {
    width: 95%;
    padding: 0 10px;
  }

  .content-footer {
    min-height: 350px;
    padding: 25px 0;
    background-attachment: scroll;
  }

  .content-footer::after {
    animation: none; /* ปิด animation บน mobile */
  }

  .content-footer .width-auto {
    grid-template-columns: 1fr;
    grid-row-gap: 25px;
    text-align: center;
  }

  .footer-site-logo img {
    width: 200px;
  }

  p.title-t {
    font-size: 18px;
    margin-bottom: 15px;
  }

  .t-footer {
    font-size: 15px;
    margin: 8px 0 12px;
  }

  .content-footer p {
    font-size: 14px;
    margin: 12px 0;
  }

  .f-tag {
    gap: 5px;
    margin-top: 10px;
    justify-content: center;
  }

  .f-tag a {
    font-size: 12px;
    padding: 5px 10px;
    border-radius: 20px;
  }

  .site-info p {
    padding: 12px 15px;
    font-size: 13px;
  }

  /* ลด effects บน mobile */
  .footer-site-logo:hover img {
    transform: scale(1.02);
  }

  .f-tag a:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 198, 255, 0.3);
  }
}

/* Mobile Small (max-width: 480px) */
@media (max-width: 480px) {
  .width-auto {
    width: 95%;
    padding: 0 8px;
  }

  .content-footer {
    min-height: 320px;
    padding: 20px 0;
  }

  .footer-site-logo img {
    width: 180px;
  }

  p.title-t {
    font-size: 16px;
    margin-bottom: 12px;
  }

  .t-footer {
    font-size: 14px;
  }

  .content-footer p {
    font-size: 13px;
  }

  .f-tag a {
    font-size: 11px;
    padding: 4px 8px;
  }

  .site-info p {
    padding: 10px 12px;
    font-size: 12px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .content-footer::before {
    background: linear-gradient(135deg,
      rgba(0, 0, 0, 0.9) 0%,
      rgba(0, 0, 0, 0.8) 30%,
      rgba(0, 0, 0, 0.7) 70%,
      rgba(0, 0, 0, 0.9) 100%);
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .site-footer,
  .content-footer .content,
  .footer-tag {
    animation: none;
  }

  .content-footer::after {
    animation: none;
  }

  .f-tag a:hover {
    animation: none;
  }
}
