/* Modern Footer CSS - Updated Version */
/* CSS Variables for easy customization */
:root {
  --footer-bg-image: url(http://mv-tv.com/wp-content/uploads/2024/09/inside-out-2-5422x2160-17820-scaled.jpg);
  --footer-overlay: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 50%, rgba(0, 0, 0, 0.8) 100%);
  --footer-primary-color: #00c6ff;
  --footer-secondary-color: #008be5;
  --footer-tertiary-color: #0056b3;
  --footer-success-color: #28af64;
  --footer-text-light: #c7c7c7;
  --footer-text-muted: #989799;
  --footer-text-dark: #999;
  --footer-bg-dark: #212120;
  --footer-border-color: #2a2936;
  --footer-border-light: #303030;
  --footer-white: #fff;
  --footer-black: #000;
  --footer-max-width: 1440px;
  --footer-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --footer-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Modern keyframe animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Apply animations to footer elements */
.site-footer {
  animation: fadeInUp 0.8s ease-out;
}

.footer-site-logo {
  animation: slideInLeft 0.6s ease-out 0.2s both;
}

.content p {
  animation: slideInRight 0.6s ease-out 0.4s both;
}

.f-tag {
  animation: fadeInUp 0.6s ease-out 0.6s both;
}

.f-tag a,
.f-tag span {
  animation: fadeInUp 0.4s ease-out calc(0.8s + var(--delay, 0s)) both;
}

.f-tag a:nth-child(1) { --delay: 0s; }
.f-tag a:nth-child(2) { --delay: 0.1s; }
.f-tag a:nth-child(3) { --delay: 0.2s; }
.f-tag a:nth-child(4) { --delay: 0.3s; }
.f-tag a:nth-child(5) { --delay: 0.4s; }
.f-tag a:nth-child(6) { --delay: 0.5s; }

.footer-menu {
  animation: fadeInUp 0.6s ease-out 1s both;
}

.site-info {
  animation: fadeInUp 0.6s ease-out 1.2s both;
}

/* Hover effects with micro-interactions */
.footer-site-logo a {
  position: relative;
  overflow: hidden;
}

.footer-site-logo a::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

.footer-site-logo a:hover::before {
  left: 100%;
}

/* Tag hover effects */
.f-tag a {
  position: relative;
  overflow: hidden;
}

.f-tag a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.f-tag a:hover::before {
  transform: translateX(100%);
}

/* Menu item effects */
.footer-menu li {
  position: relative;
}

.footer-menu a {
  position: relative;
  overflow: hidden;
}

.footer-menu a::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(79, 70, 229, 0.2) 0%, transparent 70%);
  transition: all 0.4s ease;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}

.footer-menu a:hover::before {
  width: 200px;
  height: 200px;
}

/* Floating animation for tags */
.f-tag a:hover {
  animation: float 2s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(-1px);
  }
  50% {
    transform: translateY(-3px);
  }
}

/* Glowing effect for logo */
.footer-site-logo a:hover {
  box-shadow: 
    0 0 20px rgba(79, 70, 229, 0.3),
    0 0 40px rgba(79, 70, 229, 0.2),
    0 0 60px rgba(79, 70, 229, 0.1);
}

/* Parallax effect for footer background */
.site-footer {
  background-attachment: fixed;
  background-size: cover;
  background-position: center;
}

/* Intersection Observer animations */
.fade-in-on-scroll {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease-out;
}

.fade-in-on-scroll.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Loading skeleton effect */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Responsive animations */
@media (max-width: 768px) {
  .site-footer {
    animation-duration: 0.6s;
  }
  
  .footer-site-logo,
  .content p,
  .f-tag {
    animation-duration: 0.4s;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .site-footer,
  .footer-site-logo,
  .content p,
  .f-tag,
  .f-tag a,
  .footer-menu,
  .site-info {
    animation: none;
  }
  
  .f-tag a:hover {
    animation: none;
  }
}

/* Print styles */
@media print {
  .site-footer {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  .f-tag a,
  .footer-menu a {
    color: black !important;
    text-decoration: underline !important;
  }
}
