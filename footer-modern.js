// Modern Footer JavaScript Enhancements

document.addEventListener('DOMContentLoaded', function() {
    
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    // Observe footer elements
    const footerElements = document.querySelectorAll('.site-footer .content, .f-tag, .footer-menu, .site-info');
    footerElements.forEach(el => {
        el.classList.add('fade-in-on-scroll');
        observer.observe(el);
    });

    // Smooth scroll for footer links
    const footerLinks = document.querySelectorAll('.footer-menu a[href^="#"]');
    footerLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Dynamic tag generation with animation
    function animateTagsSequentially() {
        const tags = document.querySelectorAll('.f-tag a, .f-tag span');
        tags.forEach((tag, index) => {
            tag.style.animationDelay = `${0.8 + (index * 0.1)}s`;
        });
    }

    // Parallax effect for footer background
    function handleParallax() {
        const footer = document.querySelector('.site-footer');
        if (!footer) return;

        const scrolled = window.pageYOffset;
        const parallaxSpeed = 0.5;
        
        footer.style.transform = `translateY(${scrolled * parallaxSpeed}px)`;
    }

    // Throttled scroll handler
    let ticking = false;
    function updateParallax() {
        if (!ticking) {
            requestAnimationFrame(() => {
                handleParallax();
                ticking = false;
            });
            ticking = true;
        }
    }

    // Add scroll listener for parallax (only if user prefers motion)
    if (!window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        window.addEventListener('scroll', updateParallax);
        animateTagsSequentially();
    }

    // Dynamic theme switching
    function initThemeToggle() {
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)');
        
        function updateTheme(e) {
            const footer = document.querySelector('.site-footer');
            if (footer) {
                footer.setAttribute('data-theme', e.matches ? 'dark' : 'light');
            }
        }

        prefersDark.addListener(updateTheme);
        updateTheme(prefersDark);
    }

    // Lazy loading for footer images
    function initLazyLoading() {
        const images = document.querySelectorAll('.footer-site-logo img');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.classList.remove('loading-skeleton');
                            imageObserver.unobserve(img);
                        }
                    }
                });
            });

            images.forEach(img => {
                if (img.dataset.src) {
                    img.classList.add('loading-skeleton');
                    imageObserver.observe(img);
                }
            });
        }
    }

    // Add ripple effect to clickable elements
    function addRippleEffect() {
        const clickableElements = document.querySelectorAll('.f-tag a, .footer-menu a, .footer-site-logo a');
        
        clickableElements.forEach(element => {
            element.addEventListener('click', function(e) {
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s ease-out;
                    pointer-events: none;
                `;
                
                this.style.position = 'relative';
                this.style.overflow = 'hidden';
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    }

    // Add CSS for ripple animation
    const rippleCSS = `
        @keyframes ripple {
            to {
                transform: scale(2);
                opacity: 0;
            }
        }
    `;
    
    const style = document.createElement('style');
    style.textContent = rippleCSS;
    document.head.appendChild(style);

    // Performance monitoring
    function measurePerformance() {
        if ('performance' in window) {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    console.log('Footer load time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
                }, 0);
            });
        }
    }

    // Initialize all features
    initThemeToggle();
    initLazyLoading();
    addRippleEffect();
    measurePerformance();

    // Accessibility improvements
    function enhanceAccessibility() {
        // Add skip link for footer
        const skipLink = document.createElement('a');
        skipLink.href = '#colophon';
        skipLink.textContent = 'Skip to footer';
        skipLink.className = 'skip-link';
        skipLink.style.cssText = `
            position: absolute;
            left: -9999px;
            z-index: 999999;
            padding: 8px 16px;
            background: #000;
            color: #fff;
            text-decoration: none;
        `;
        
        skipLink.addEventListener('focus', function() {
            this.style.left = '6px';
            this.style.top = '7px';
        });
        
        skipLink.addEventListener('blur', function() {
            this.style.left = '-9999px';
        });
        
        document.body.insertBefore(skipLink, document.body.firstChild);

        // Add ARIA labels
        const footer = document.querySelector('.site-footer');
        if (footer && !footer.getAttribute('aria-label')) {
            footer.setAttribute('aria-label', 'Site footer');
        }

        // Improve focus management
        const focusableElements = footer.querySelectorAll('a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])');
        focusableElements.forEach((element, index) => {
            if (!element.getAttribute('tabindex')) {
                element.setAttribute('tabindex', '0');
            }
        });
    }

    enhanceAccessibility();

    // Error handling
    window.addEventListener('error', function(e) {
        console.warn('Footer script error:', e.error);
    });

    console.log('Modern footer enhancements loaded successfully!');
});
