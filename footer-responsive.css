/* Responsive Footer CSS - Mobile & Tablet Optimization */

/* Keyframes for mobile animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes mobileFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

/* Large Desktop (1440px+) */
@media (min-width: 1440px) {
  .width-auto {
    width: var(--footer-max-width);
  }
  
  .footer-site-logo img {
    width: 320px;
  }
  
  p.title-t {
    font-size: 26px;
  }
  
  .t-footer {
    font-size: 20px;
  }
}

/* Desktop (1200px - 1439px) */
@media (max-width: 1439px) and (min-width: 1200px) {
  .width-auto {
    width: 95%;
    max-width: 1200px;
  }
  
  .content-footer .width-auto {
    grid-column-gap: 4%;
  }
}

/* Laptop (992px - 1199px) */
@media (max-width: 1199px) and (min-width: 992px) {
  .width-auto {
    width: 95%;
    max-width: 1000px;
  }
  
  .content-footer .width-auto {
    grid-template-columns: 45% 50%;
    grid-column-gap: 5%;
  }
  
  .footer-site-logo img {
    width: 280px;
  }
  
  p.title-t {
    font-size: 22px;
  }
}

/* Tablet (768px - 991px) */
@media (max-width: 991px) and (min-width: 768px) {
  :root {
    --footer-max-width: 100%;
  }
  
  .width-auto {
    width: 95%;
    padding: 0 15px;
  }
  
  .content-footer {
    min-height: 400px;
    padding: 30px 0;
    background-attachment: scroll; /* ปิด parallax บน tablet */
  }
  
  .content-footer .width-auto {
    grid-template-columns: 1fr;
    grid-row-gap: 30px;
    text-align: center;
  }
  
  .footer-site-logo {
    margin-bottom: 20px;
  }
  
  .footer-site-logo img {
    width: 250px;
  }
  
  p.title-t {
    font-size: 20px;
    text-align: center;
  }
  
  .t-footer {
    font-size: 16px;
    text-align: center;
  }
  
  .content-footer p {
    text-align: center;
    font-size: 15px;
  }
  
  .f-tag {
    justify-content: center;
    gap: 6px;
  }
  
  .f-tag a {
    font-size: 13px;
    padding: 6px 12px;
  }
  
  .footer-tag {
    margin-top: 0;
  }
}

/* Mobile Large (576px - 767px) */
@media (max-width: 767px) and (min-width: 576px) {
  .width-auto {
    width: 95%;
    padding: 0 10px;
  }
  
  .content-footer {
    min-height: 350px;
    padding: 25px 0;
    background-attachment: scroll;
  }
  
  .content-footer .width-auto {
    grid-template-columns: 1fr;
    grid-row-gap: 25px;
    text-align: center;
  }
  
  .footer-site-logo img {
    width: 220px;
  }
  
  p.title-t {
    font-size: 18px;
    margin-bottom: 15px;
  }
  
  .t-footer {
    font-size: 15px;
    margin: 8px 0 12px;
  }
  
  .content-footer p {
    font-size: 14px;
    margin: 12px 0;
  }
  
  .f-tag {
    gap: 5px;
    margin-top: 10px;
  }
  
  .f-tag a {
    font-size: 12px;
    padding: 5px 10px;
    border-radius: 20px;
  }
  
  .site-info p {
    padding: 12px 15px;
    font-size: 13px;
  }
  
  /* ปรับ animation สำหรับ mobile */
  .content-footer .content {
    animation: slideInUp 0.5s ease-out 0.2s both;
  }
  
  .footer-tag {
    animation: slideInUp 0.5s ease-out 0.4s both;
  }
}

/* Mobile Small (320px - 575px) */
@media (max-width: 575px) {
  .width-auto {
    width: 95%;
    padding: 0 8px;
  }
  
  .content-footer {
    min-height: 320px;
    padding: 20px 0;
    background-attachment: scroll;
  }
  
  .content-footer::after {
    animation: mobileFloat 4s ease-in-out infinite; /* ลด animation บน mobile */
  }
  
  .footer-site-logo {
    margin-bottom: 15px;
  }
  
  .footer-site-logo img {
    width: 180px;
    max-width: 90%;
  }
  
  p.title-t {
    font-size: 16px;
    margin-bottom: 12px;
    padding-bottom: 8px;
  }
  
  .t-footer {
    font-size: 14px;
    margin: 6px 0 10px;
  }
  
  .content-footer p {
    font-size: 13px;
    margin: 10px 0;
    line-height: 1.5;
  }
  
  .f-tag {
    gap: 4px;
    margin-top: 8px;
  }
  
  .f-tag a {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 15px;
    margin: 2px;
  }
  
  .site-info p {
    padding: 10px 12px;
    font-size: 12px;
    line-height: 1.4;
  }
  
  /* ลด shadow effects บน mobile เพื่อประสิทธิภาพ */
  .footer-site-logo img {
    filter: none;
  }
  
  .footer-site-logo:hover img {
    transform: scale(1.02);
    filter: brightness(1.1);
  }
  
  .f-tag a:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 198, 255, 0.3);
  }
}

/* Extra Small Mobile (max 319px) */
@media (max-width: 319px) {
  .width-auto {
    width: 98%;
    padding: 0 5px;
  }
  
  .content-footer {
    min-height: 300px;
    padding: 15px 0;
  }
  
  .footer-site-logo img {
    width: 150px;
  }
  
  p.title-t {
    font-size: 15px;
  }
  
  .t-footer {
    font-size: 13px;
  }
  
  .content-footer p {
    font-size: 12px;
  }
  
  .f-tag a {
    font-size: 10px;
    padding: 3px 6px;
  }
  
  .site-info p {
    padding: 8px 10px;
    font-size: 11px;
  }
}

/* Landscape orientation adjustments */
@media (max-height: 500px) and (orientation: landscape) {
  .content-footer {
    min-height: 250px;
    padding: 15px 0;
  }
  
  .footer-site-logo {
    margin-bottom: 10px;
  }
  
  .footer-site-logo img {
    width: 120px;
  }
  
  p.title-t {
    font-size: 14px;
    margin-bottom: 8px;
  }
  
  .content-footer p {
    font-size: 11px;
    margin: 5px 0;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .footer-site-logo img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark mode responsive adjustments */
@media (prefers-color-scheme: dark) {
  .content-footer::before {
    background: linear-gradient(
      135deg, 
      rgba(0, 0, 0, 0.9) 0%, 
      rgba(0, 0, 0, 0.8) 30%,
      rgba(0, 0, 0, 0.7) 70%,
      rgba(0, 0, 0, 0.9) 100%
    );
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .content-footer::after {
    animation: none;
  }
  
  .content-footer .content,
  .footer-tag {
    animation: none;
  }
  
  .f-tag a:hover {
    animation: none;
  }
  
  .footer-site-logo:hover img {
    transform: none;
  }
}
