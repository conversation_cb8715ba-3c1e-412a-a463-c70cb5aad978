/* Modern Footer Styles */
:root {
  --footer-bg: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  --footer-text: #e8e8e8;
  --footer-text-muted: #a0a0a0;
  --footer-accent: #4f46e5;
  --footer-accent-hover: #6366f1;
  --footer-border: rgba(255, 255, 255, 0.1);
  --footer-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
  --footer-spacing: clamp(1rem, 4vw, 2rem);
  --footer-max-width: 1200px;
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Main Footer Container */
.site-footer {
  background: var(--footer-bg);
  color: var(--footer-text);
  position: relative;
  overflow: hidden;
  box-shadow: var(--footer-shadow);
  margin-top: auto;
}

.site-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--footer-border), transparent);
}

/* Content Footer Section */
.content-footer {
  padding: var(--footer-spacing) 0;
  border-bottom: 1px solid var(--footer-border);
}

.width-auto {
  max-width: var(--footer-max-width);
  margin: 0 auto;
  padding: 0 var(--footer-spacing);
}

.content {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: var(--footer-spacing);
  align-items: center;
  margin-bottom: calc(var(--footer-spacing) * 1.5);
}

/* Footer Logo */
.footer-site-logo {
  display: flex;
  align-items: center;
}

.footer-site-logo a {
  display: inline-block;
  transition: var(--transition-smooth);
  text-decoration: none;
  border-radius: 8px;
  padding: 0.5rem;
}

.footer-site-logo a:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.footer-site-logo img,
.footer-site-logo svg {
  max-height: 40px;
  width: auto;
  filter: brightness(1.1);
  transition: var(--transition-smooth);
}

.footer-site-logo a:hover img,
.footer-site-logo a:hover svg {
  filter: brightness(1.3);
}

/* Footer Description */
.content p {
  margin: 0;
  line-height: 1.6;
  color: var(--footer-text-muted);
  font-size: 0.95rem;
}

/* Tags Section */
.f-tag {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: 1rem;
}

.f-tag a,
.f-tag span {
  display: inline-block;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--footer-border);
  border-radius: 20px;
  color: var(--footer-text);
  text-decoration: none;
  font-size: 0.85rem;
  font-weight: 500;
  transition: var(--transition-smooth);
  backdrop-filter: blur(10px);
}

.f-tag a:hover {
  background: var(--footer-accent);
  border-color: var(--footer-accent);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

/* Footer Menu */
.footer-menu {
  padding: calc(var(--footer-spacing) * 0.75) 0;
  border-bottom: 1px solid var(--footer-border);
}

.footer-menu ul {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  list-style: none;
  margin: 0;
  padding: 0;
  max-width: var(--footer-max-width);
  margin: 0 auto;
  padding: 0 var(--footer-spacing);
}

.footer-menu a {
  color: var(--footer-text);
  text-decoration: none;
  font-weight: 500;
  position: relative;
  transition: var(--transition-smooth);
  padding: 0.5rem 0;
}

.footer-menu a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--footer-accent);
  transition: var(--transition-smooth);
}

.footer-menu a:hover {
  color: var(--footer-accent-hover);
}

.footer-menu a:hover::after {
  width: 100%;
}

/* Site Info */
.site-info {
  padding: calc(var(--footer-spacing) * 0.75) var(--footer-spacing);
  text-align: center;
  background: rgba(0, 0, 0, 0.2);
}

.site-info p {
  margin: 0;
  color: var(--footer-text-muted);
  font-size: 0.85rem;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 1rem;
  }
  
  .footer-menu ul {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .f-tag {
    justify-content: center;
  }
  
  :root {
    --footer-spacing: 1rem;
  }
}

@media (max-width: 480px) {
  .f-tag a,
  .f-tag span {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
  
  .footer-site-logo img,
  .footer-site-logo svg {
    max-height: 32px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --footer-bg: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);
    --footer-text: #f0f0f0;
    --footer-text-muted: #b0b0b0;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for accessibility */
.footer-site-logo a:focus,
.f-tag a:focus,
.footer-menu a:focus {
  outline: 2px solid var(--footer-accent);
  outline-offset: 2px;
  border-radius: 4px;
}
